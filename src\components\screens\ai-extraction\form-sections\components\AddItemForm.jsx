import React, { useEffect, useState, useCallback } from "react";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Checkbox,
  FormControlLabel,
  Button,
  CircularProgress,
} from "@mui/material";
import { NumericFormat } from "react-number-format";
import { Formik, Field } from "formik";
import * as yup from "yup";
import { useAuth } from "../../../../../contexts/AuthContext";
import apiClient from "../../../../services/apiClient";
import { resturls } from "../../../../utils/apiurls";

// Yup validation schema
const validationSchema = yup.object().shape({
  name: yup.string().required("Name is required"),
  rate: yup
    .string()
    .required("Rate is required")
    .test("is-number", "Rate must be a valid number", (value) => {
      return value && !isNaN(parseFloat(value)) && parseFloat(value) > 0;
    }),
  account_id: yup.string().required("Account is required"),
  tax_id: yup.string().required("Tax is required"),
  hsn_or_sac: yup.string().required("HSN/SAC is required"),
  description: yup.string(),
  item_type: yup.string().required("Item type is required"),
  unit: yup.string(),
  is_taxable: yup.boolean(),
  purchase_rate: yup
    .string()
    .test("is-number", "Purchase rate must be a valid number", (value) => {
      return !value || (!isNaN(parseFloat(value)) && parseFloat(value) > 0);
    }),
  product_type: yup.string().required("Product type is required"),
});

// Item type options
const itemTypeOptions = [
  { key: "sales", text: "Sales", value: "sales" },
  { key: "purchase", text: "Purchase", value: "purchase" },
  {
    key: "sales_and_purchase",
    text: "Sales and Purchase",
    value: "sales_and_purchase",
  },
];

// Product type options
const productTypeOptions = [
  { key: "goods", text: "Goods", value: "goods" },
  { key: "services", text: "Services", value: "services" },
];

// Initial form values
const initialValues = {
  name: "",
  rate: "",
  description: "",
  account_id: "",
  tax_id: "",
  item_type: "sales",
  unit: "",
  hsn_or_sac: "",
  is_taxable: true,
  purchase_rate: "",
  product_type: "goods",
};

function AddItemForm({ open, onClose, onSave = null }) {
  const { globSelectedBusiness } = useAuth();
  const businessId = globSelectedBusiness?.business_id;

  const [accounts, setAccounts] = useState([]);
  const [taxes, setTaxes] = useState([]);
  const [isLoading, setIsLoading] = useState(false);

  // Fetch accounts from API
  const fetchAccounts = useCallback(async () => {
    setIsLoading(true);
    try {
      const response = await apiClient.get(
        `${resturls.leadgerscreenitems}?business_id=${businessId}`
      );
      const accountsList = response.data.map((account) => ({
        key: account.id,
        text: account.ledger_name || account.name,
        value: account.id,
      }));
      setAccounts(accountsList);
    } catch (error) {
      console.error("Error fetching accounts:", error);
    } finally {
      setIsLoading(false);
    }
  }, [businessId]);

  // Fetch taxes from API
  const fetchTaxes = useCallback(async () => {
    setIsLoading(true);
    try {
      const response = await apiClient.get(
        `${resturls.TaxledgersItems}?business_id=${businessId}`
      );
      const taxesList = response.data.map((tax) => ({
        key: tax.id,
        text: `${tax.ledger} (${tax.tax_rate}%)`,
        value: tax.id,
      }));
      setTaxes(taxesList);
    } catch (error) {
      console.error("Error fetching taxes:", error);
    } finally {
      setIsLoading(false);
    }
  }, [businessId]);

  // Fetch accounts and taxes on component mount
  useEffect(() => {
    if (open && businessId) {
      fetchAccounts();
      fetchTaxes();
    }
  }, [open, businessId, fetchAccounts, fetchTaxes]);

  // Handle form submission
  const handleFormSubmit = (values, { resetForm }) => {
    // addItem(organization_id, values).then((res) => {
    //   console.log("res ::: ", res);
    // });
    onSave && onSave(values);
    resetForm();
    onClose && onClose();
  };

  return (
    <Dialog
      open={open}
      onClose={() => onClose && onClose()}
      maxWidth="md"
      fullWidth
      clam
    >
      <DialogTitle>Add New Item</DialogTitle>
      <DialogContent>
        <Formik
          initialValues={initialValues}
          validationSchema={validationSchema}
          onSubmit={handleFormSubmit}
          enableReinitialize
          validateOnMount={true}
        >
          {({
            values,
            errors,
            touched,
            handleSubmit,
            setFieldValue,
            isValid,
          }) => {
            // Check if required fields are filled
            const isFormReady =
              values.name &&
              values.rate &&
              values.account_id &&
              values.tax_id &&
              values.hsn_or_sac &&
              isValid;

            return (
              <>
                <form
                  onSubmit={handleSubmit}
                  id="add-item-form"
                  className="relative"
                >
                  {isLoading && (
                    <div className="absolute inset-0 flex items-center justify-center bg-white bg-opacity-70 z-10">
                      <CircularProgress />
                    </div>
                  )}

                  {/* First row: Name, Rate, HSN/SAC, Unit */}
                  <div className="grid grid-cols-1 md:grid-cols-4 gap-3 mb-4">
                    <Field name="name">
                      {({ field }) => (
                        <TextField
                          {...field}
                          label="Name"
                          placeholder="Enter item name"
                          fullWidth
                          required
                          error={!!(errors.name && touched.name)}
                          size="small"
                        />
                      )}
                    </Field>

                    <Field name="rate">
                      {({ field }) => (
                        <NumericFormat
                          customInput={TextField}
                          thousandSeparator={true}
                          decimalScale={2}
                          fixedDecimalScale
                          placeholder="0.00"
                          value={field.value}
                          onValueChange={(values) =>
                            setFieldValue("rate", values.value)
                          }
                        />
                      )}
                    </Field>

                    <Field name="hsn_or_sac">
                      {({ field }) => (
                        <TextField
                          {...field}
                          label="HSN/SAC"
                          placeholder="Enter HSN/SAC code"
                          fullWidth
                          required
                          error={!!(errors.hsn_or_sac && touched.hsn_or_sac)}
                          size="small"
                        />
                      )}
                    </Field>

                    <Field name="unit">
                      {({ field }) => (
                        <TextField
                          {...field}
                          label="Unit"
                          placeholder="e.g., pcs, kg, m"
                          fullWidth
                          size="small"
                        />
                      )}
                    </Field>
                  </div>

                  {/* Second row: Account, Tax, Item Type, Product Type */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-4">
                    <Field name="account_id">
                      {({ field }) => (
                        <FormControl
                          fullWidth
                          required
                          error={!!(errors.account_id && touched.account_id)}
                          size="small"
                        >
                          <InputLabel>Account</InputLabel>
                          <Select
                            {...field}
                            label="Account"
                            value={field.value || ""}
                            onChange={(e) =>
                              setFieldValue("account_id", e.target.value)
                            }
                          >
                            {accounts.map((account) => (
                              <MenuItem key={account.key} value={account.value}>
                                {account.text}
                              </MenuItem>
                            ))}
                          </Select>
                        </FormControl>
                      )}
                    </Field>

                    <Field name="tax_id">
                      {({ field }) => (
                        <FormControl
                          fullWidth
                          required
                          error={!!(errors.tax_id && touched.tax_id)}
                          size="small"
                        >
                          <InputLabel>Tax</InputLabel>
                          <Select
                            {...field}
                            label="Tax"
                            value={field.value || ""}
                            onChange={(e) =>
                              setFieldValue("tax_id", e.target.value)
                            }
                          >
                            {taxes.map((tax) => (
                              <MenuItem key={tax.key} value={tax.value}>
                                {tax.text}
                              </MenuItem>
                            ))}
                          </Select>
                        </FormControl>
                      )}
                    </Field>

                    <Field name="item_type">
                      {({ field }) => (
                        <FormControl fullWidth size="small">
                          <InputLabel>Item Type</InputLabel>
                          <Select
                            {...field}
                            label="Item Type"
                            value={field.value || ""}
                            onChange={(e) =>
                              setFieldValue("item_type", e.target.value)
                            }
                          >
                            {itemTypeOptions.map((option) => (
                              <MenuItem key={option.key} value={option.value}>
                                {option.text}
                              </MenuItem>
                            ))}
                          </Select>
                        </FormControl>
                      )}
                    </Field>

                    <Field name="product_type">
                      {({ field }) => (
                        <FormControl fullWidth size="small">
                          <InputLabel>Product Type</InputLabel>
                          <Select
                            {...field}
                            label="Product Type"
                            value={field.value || ""}
                            onChange={(e) =>
                              setFieldValue("product_type", e.target.value)
                            }
                          >
                            {productTypeOptions.map((option) => (
                              <MenuItem key={option.key} value={option.value}>
                                {option.text}
                              </MenuItem>
                            ))}
                          </Select>
                        </FormControl>
                      )}
                    </Field>
                  </div>

                  {/* Description row */}
                  <div className="mb-4">
                    <Field name="description">
                      {({ field }) => (
                        <TextField
                          {...field}
                          label="Description"
                          placeholder="Enter description"
                          fullWidth
                          multiline
                          rows={3}
                          size="small"
                        />
                      )}
                    </Field>
                  </div>

                  {/* Conditional Purchase Rate field */}
                  {(values.item_type === "purchase" ||
                    values.item_type === "sales_and_purchase") && (
                    <div className="mb-4">
                      <Field name="purchase_rate">
                        {({ field }) => (
                          <NumericFormat
                            customInput={TextField}
                            thousandSeparator={true}
                            decimalScale={2}
                            fixedDecimalScale
                            placeholder="0.00"
                            value={field.value}
                            onValueChange={(values) =>
                              setFieldValue("purchase_rate", values.value)
                            }
                            label="Purchase Rate"
                            fullWidth
                            size="small"
                          />
                        )}
                      </Field>
                    </div>
                  )}

                  {/* Is Taxable checkbox */}
                  <div className="mb-4">
                    <Field name="is_taxable">
                      {({ field }) => (
                        <FormControlLabel
                          control={
                            <Checkbox
                              checked={field.value}
                              onChange={(e) =>
                                setFieldValue("is_taxable", e.target.checked)
                              }
                            />
                          }
                          label="Is Taxable"
                        />
                      )}
                    </Field>
                  </div>
                </form>

                {/* Dialog Actions */}
                <DialogActions>
                  <Button onClick={() => onClose && onClose()} color="inherit">
                    Cancel
                  </Button>
                  <Button
                    onClick={() => handleSubmit()}
                    variant="contained"
                    color="primary"
                    disabled={!isFormReady}
                    type="submit"
                  >
                    Add Item
                  </Button>
                </DialogActions>
              </>
            );
          }}
        </Formik>
      </DialogContent>
    </Dialog>
  );
}

export default AddItemForm;
